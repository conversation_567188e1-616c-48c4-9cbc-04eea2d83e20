# log dir
log_dir: ./logs/benchv2/tall/trainOnFS

# model setting
pretrained: null   # path to a pre-trained model, if using one
model_name: tall   # model name

# dataset
all_dataset: [FaceForensics++, FF-F2F, FF-DF, FF-FS, FF-NT, FaceShifter, DeepFakeDetection, Celeb-DF-v1, Celeb-DF-v2, DFDCP, DFDC, DeeperForensics-1.0, UADFV]
train_dataset: [FaceForensics++]
test_dataset: [Celeb-DF-v2]

compression: c23  # compression-level for videos
train_batchSize: 32   # training batch size
test_batchSize: 32   # test batch size
workers: 4   # number of data loading workers
frame_num: {'train': 32, 'test': 32}   # number of frames to use per video in training and testing
resolution: 224   # resolution of output image to network
with_mask: false   # whether to include mask information in the input
with_landmark: false   # whether to include facial landmark information in the input
video_mode: True  # whether to use video-level data
clip_size: 4  # number of frames in each clip, should be square number of an integer
dataset_type: tall


# data augmentation
use_data_augmentation: true  # Add this flag to enable/disable data augmentation
data_aug:
  flip_prob: 0.5
  rotate_prob: 0.5
  rotate_limit: [-10, 10]
  blur_prob: 0.5
  blur_limit: [3, 7]
  brightness_prob: 0.5
  brightness_limit: [-0.1, 0.1]
  contrast_limit: [-0.1, 0.1]
  quality_lower: 40
  quality_upper: 100

# mean and std for normalization
mean: [0.485, 0.456, 0.406]
std: [0.229, 0.224, 0.225]

# optimizer config
optimizer:
  # choose between 'adam' and 'sgd'
  type: adam
  adam:
    lr: 0.00002  # learning rate
    beta1: 0.9  # beta1 for Adam optimizer
    beta2: 0.999 # beta2 for Adam optimizer
    eps: 0.00000001  # epsilon for Adam optimizer
    weight_decay: 0.0005  # weight decay for regularization
    amsgrad: false
  sgd:
    lr: 0.0002  # learning rate
    momentum: 0.9  # momentum for SGD optimizer
    weight_decay: 0.0005  # weight decay for regularization

# training config
lr_scheduler: null   # learning rate scheduler
nEpochs: 100   # number of epochs to train for
start_epoch: 0   # manual epoch number (useful for restarts)
save_epoch: 1   # interval epochs for saving models
rec_iter: 100   # interval iterations for recording
logdir: ./logs   # folder to output images and logs
manualSeed: 1024   # manual seed for random number generation
save_ckpt: true   # whether to save checkpoint
save_feat: true   # whether to save features

# loss function
loss_func: cross_entropy   # loss function to use
losstype: null

# metric
metric_scoring: auc   # metric for evaluation (auc, acc, eer, ap)

# cuda

cuda: true   # whether to use CUDA acceleration
cudnn: true   # whether to use CuDNN for convolution operations

dataset_json_folder: 'datasets/dataset_json'
rgb_dir: 'datasets/rgb'
label_dict:
  # DFD
  DFD_fake: 1
  DFD_real: 0
  # FF++ + FaceShifter(FF-real+FF-FH)
  FF-SH: 1
  FF-F2F: 1
  FF-DF: 1
  FF-FS: 1
  FF-NT: 1
  FF-FH: 1
  FF-real: 0
  # CelebDF
  CelebDFv1_real: 0
  CelebDFv1_fake: 1
  CelebDFv2_real: 0
  CelebDFv2_fake: 1
  # DFDCP
  DFDCP_Real: 0
  DFDCP_FakeA: 1
  DFDCP_FakeB: 1
  # DFDC
  DFDC_Fake: 1
  DFDC_Real: 0
  # DeeperForensics-1.0
  DF_fake: 1
  DF_real: 0
  # UADFV
  UADFV_Fake: 1
  UADFV_Real: 0
  # Roop
  roop_Real: 0
  roop_Fake: 1


mask_grid_size: 16

num_classes: 2
embed_dim: 128
mlp_ratio: 4.0
patch_size: 4
window_size: [14, 14, 14, 7]
depths: [2, 2, 18, 2]
num_heads: [4, 8, 16, 32]
ape: true # use absolution position embedding
thumbnail_rows: 2
drop_rate: 0
drop_path_rate: 0.1

task_target: TALL

