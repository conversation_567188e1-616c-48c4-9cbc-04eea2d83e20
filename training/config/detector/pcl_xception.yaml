# log dir 
log_dir: logs/evaluations/pcl_xception_best

#LMDB dir
lmdb_dir: 'I:\transform_2_lmdb'

# model setting
pretrained: ./training/pretrained/xception-b5690688.pth #./training/pretrained/xception-b5690688.pth   # path to a pre-trained model, if using one
model_name: pcl_xception   # model name
backbone_name: xception_sladd  # backbone name
restore_ckpt: 'None'
#backbone setting
backbone_config:
  mode: original
  num_classes: 2
  inc: 3
  dropout: false

# dataset
all_dataset: [FaceForensics++, FF-F2F, FF-DF, FF-FS, FF-NT, FaceShifter, DeepFakeDetection, Celeb-DF-v1, Celeb-DF-v2, DFDCP, DFDC, DeeperForensics-1.0, UADFV]
train_dataset: [FaceForensics++]
test_dataset: [FaceForensics++, FF-F2F, FF-DF, FF-FS, FF-NT]
dataset_type: I2G
compression: c23  # compression-level for videos
train_batchSize: 32   # training batch size
test_batchSize: 32   # test batch size
workers: 8   # number of data loading workers
frame_num: {'train': 8, 'test': 32}   # number of frames to use per video in training and testing
resolution: 256   # resolution of output image to network
with_mask: false   # whether to include mask information in the input
with_landmark: true   # whether to include facial landmark information in the input


# data augmentation
use_data_augmentation: true  # Add this flag to enable/disable data augmentation
data_aug:
  flip_prob: 0.5
  rotate_prob: 0
  rotate_limit: [-10, 10]
  blur_prob: 0.5
  blur_limit: [3, 7]
  brightness_prob: 0.5
  brightness_limit: [-0.1, 0.1]
  contrast_limit: [-0.1, 0.1]
  quality_lower: 40
  quality_upper: 100
  remove_attribute: false
  gridmask_prob: 0
  remove_nose_prob: 0.0
  remove_eyes_prob: 0.0


# mean and std for normalization
mean: [0.5, 0.5, 0.5]
std: [0.5, 0.5, 0.5]

# optimizer config
optimizer:
  # choose between 'adam' and 'sgd'
  type: adam
  adam:
    lr: 0.0002  # learning rate
    beta1: 0.9  # beta1 for Adam optimizer
    beta2: 0.999 # beta2 for Adam optimizer
    eps: 0.00000001  # epsilon for Adam optimizer
    weight_decay: 0.0005  # weight decay for regularization
    amsgrad: false
  sgd:
    lr: 0.0002  # learning rate
    momentum: 0.9  # momentum for SGD optimizer
    weight_decay: 0.0005  # weight decay for regularization
  sam:
    lr: 0.001  # learning rate
    momentum: 0.9  # momentum for SGD optimizer

# training config
lr_scheduler: step   # learning rate scheduler step
lr_step: 3
lr_gamma: 0.4   # learning rate scheduler
nEpochs: 150   # number of epochs to train for
start_epoch: 0   # manual epoch number (useful for restarts)
save_epoch: 1   # interval epochs for saving models
rec_iter: 100   # interval iterations for recording
logdir: ./logs   # folder to output images and logs
manualSeed: 1024   # manual seed for random number generation
save_ckpt: true   # whether to save checkpoint
save_feat: true   # whether to save features

# loss function
loss_func: cross_entropy   # loss function to use cross_entropy
pcl_loss_weight: 1 # PLW
losstype: null

# metric
metric_scoring: auc   # metric for evaluation (auc, acc, eer, ap)

# cuda

cuda: true   # whether to use CUDA acceleration
cudnn: true   # whether to use CuDNN for convolution operations
