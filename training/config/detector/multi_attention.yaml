# log dir
log_dir: log/multi_attention/

# model setting
pretrained: ./training/pretrained/efficientnet-b4-6ed6700e.pth   # path to a pre-trained model, if using one
model_name: multi_attention   # model name
backbone_name: efficientnetb4  # backbone name

#backbone setting
backbone_config:
  num_classes: 2
  inc: 3
  dropout: false
  mode: Original

# dataset
all_dataset: [FaceForensics++, FF-F2F, FF-DF, FF-FS, FF-NT, FaceShifter, DeepFakeDetection, Celeb-DF-v1, Celeb-DF-v2, DFDCP, DFDC, DeeperForensics-1.0, UADFV]
train_dataset: [FaceForensics++]
test_dataset: [FaceForensics++, Celeb-DF-v1, Celeb-DF-v2]

compression: c23  # compression-level for videos
train_batchSize: 32   # training batch size
test_batchSize: 32   # test batch size
workers: 8   # number of data loading workers
frame_num: {'train': 32, 'test': 32}   # number of frames to use per video in training and testing
resolution: 256   # resolution of output image to network
with_mask: false   # whether to include mask information in the input
with_landmark: false   # whether to include facial landmark information in the input
save_ckpt: true   # whether to save checkpoint
save_feat: true   # whether to save features


# data augmentation
use_data_augmentation: false  # Add this flag to enable/disable data augmentation
data_aug:
  flip_prob: 0.5
  rotate_prob: 0.5
  rotate_limit: [-10, 10]
  blur_prob: 0.5
  blur_limit: [3, 7]
  brightness_prob: 0.5
  brightness_limit: [-0.1, 0.1]
  contrast_limit: [-0.1, 0.1]
  quality_lower: 40
  quality_upper: 100

# mean and std for normalization
mean: [0.5, 0.5, 0.5]
std: [0.5, 0.5, 0.5]

# optimizer config
optimizer:
  # choose between 'adam' and 'sgd'
  type: adam
  adam:
    lr: 0.0002  # learning rate
    beta1: 0.9  # beta1 for Adam optimizer
    beta2: 0.999 # beta2 for Adam optimizer
    eps: 0.00000001  # epsilon for Adam optimizer
    weight_decay: 0.000001  # weight decay for regularization
    amsgrad: false
  sgd:
    lr: 0.0002  # learning rate
    momentum: 0.9  # momentum for SGD optimizer
    weight_decay: 0.0005  # weight decay for regularization

# training config
lr_scheduler: step   # learning rate scheduler
lr_step: 5
lr_gamma: 0.5
nEpochs: 50   # number of epochs to train for
start_epoch: 0   # manual epoch number (useful for restarts)
save_epoch: 1   # interval epochs for saving models
rec_iter: 100   # interval iterations for recording
logdir: ./logs   # folder to output images and logs
manualSeed: 1024   # manual seed for random number generation
save_ckpt: false   # whether to save checkpoint

# loss function
loss_func:
 cls_loss: cross_entropy   # loss function to use
 ril_loss: region_independent_loss  # Region Independent Loss
 ril_params:
   N: 32
   alpha: 0.05
   alpha_decay: 0.9
   inter_margin: 0.2
   intra_margin: [0.05, 0.1]
 weights: [1, 1]  # weights for CE_loss and RIL, respectively
losstype: null

# metric
metric_scoring: auc   # metric for evaluation (auc, acc, eer, ap)

# cuda

cuda: true   # whether to use CUDA acceleration
cudnn: true   # whether to use CuDNN for convolution operations

# model parameters
feature_layer: b2
attention_layer: b5
num_attentions: 4
mid_dim: 256
dropout_rate: 0.25
dropout_rate_final: 0.5
AGDA:
  kernel_size: 11
  dilation: 2
  sigma: 7
  threshold: [0.4, 0.6]
  zoom: [3, 5]
  scale_factor: 0.5
  noise_rate: 0.1

backbone_nEpochs: 10
batch_per_epoch: 3591