CUDNN:
  BENCHMARK: true
  DETERMINISTIC: false
  ENABLED: true
GPUS: (0,1,2,3)
OUTPUT_DIR: 'output'
LOG_DIR: 'log'
WORKERS: 4
PRINT_FREQ: 100

DATASET:
  DATASET: lip
  ROOT: 'data/'
  TEST_SET: 'list/lip/valList.txt'
  TRAIN_SET: 'list/lip/trainList.txt'
  NUM_CLASSES: 20
MODEL: 
  NAME: cls_hrnet
  #IMAGE_SIZE: 
  #  - 224
  #  - 224
  EXTRA:
    STAGE1:
      NUM_MODULES: 1
      NUM_RANCHES: 1
      BLOCK: BOTTLENECK
      NUM_BLOCKS:
      - 4
      NUM_CHANNELS:
      - 64
      FUSE_METHOD: SUM
    STAGE2:
      NUM_MODULES: 1
      NUM_BRANCHES: 2
      BLOCK: BASIC
      NUM_BLOCKS:
      - 4
      - 4
      NUM_CHANNELS:
      - 48
      - 96
      FUSE_METHOD: SUM
    STAGE3:
      NUM_MODULES: 4
      NUM_BRANCHES: 3
      BLOCK: BASIC
      NUM_BLOCKS:
      - 4
      - 4
      - 4
      NUM_CHANNELS:
      - 48
      - 96
      - 192
      FUSE_METHOD: SUM
    STAGE4:
      NUM_MODULES: 3
      NUM_BRANCHES: 4
      BLOCK: BASIC
      NUM_BLOCKS:
      - 4
      - 4
      - 4
      - 4
      NUM_CHANNELS:
      - 48
      - 96
      - 192
      - 384
      FUSE_METHOD: SUM
LOSS:
  USE_OHEM: false
  OHEMTHRES: 0.9
  OHEMKEEP: 131072
TRAIN:
  IMAGE_SIZE:
  - 473
  - 473
  BASE_SIZE: 473
  BATCH_SIZE_PER_GPU: 10
  SHUFFLE: true
  BEGIN_EPOCH: 0
  END_EPOCH: 150
  RESUME: true
  OPTIMIZER: sgd
  LR: 0.007
  WD: 0.0005
  MOMENTUM: 0.9
  NESTEROV: false
  FLIP: true
  MULTI_SCALE: true
  DOWNSAMPLERATE: 1
  IGNORE_LABEL: 255
  SCALE_FACTOR: 11
TEST:
  IMAGE_SIZE:
  - 473
  - 473
  BASE_SIZE: 473
  BATCH_SIZE_PER_GPU: 16
  NUM_SAMPLES: 2000
  FLIP_TEST: false
  MULTI_SCALE: false
